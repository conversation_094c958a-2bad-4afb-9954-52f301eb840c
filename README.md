# TaskFlow API - Simple Docker CI/CD Pipeline

A beginner-friendly CI/CD pipeline using GitHub Actions and Docker Hub for the TaskFlow task management API.

🚀 **CI/CD Pipeline Status**: Ready for testing!

## 🚀 Features

- **🔐 User Authentication**: JWT-based login and registration system
- **🐳 Docker Containerization**: Production-ready Docker setup
- **⚡ Automated CI/CD**: GitHub Actions for testing and building
- **🔒 Security Scanning**: Automated vulnerability scanning with Trivy
- **🧪 Comprehensive Testing**: Unit tests with Jest and Supertest
- **📦 Docker Hub Integration**: Automatic image building and publishing

## 📁 Project Structure

```
taskflow-api/
├── backend/                    # Node.js backend service
│   ├── src/
│   │   ├── index.js           # Main application file
│   │   ├── index.test.js      # API tests
│   │   ├── middleware/        # Authentication middleware
│   │   ├── models/            # User model
│   │   └── routes/            # Authentication routes
│   ├── Dockerfile             # Docker configuration
│   └── package.json           # Node.js dependencies
├── .github/
│   └── workflows/
│       ├── ci-cd.yml          # Main CI/CD pipeline
│       └── manual-deploy.yml  # Manual Docker build
├── docker-compose.yml         # Production compose file
├── docker-compose.dev.yml     # Development compose file
├── .env.example               # Environment variables template
└── README.md                  # This file
```

## 🛠️ Quick Start

### Prerequisites

- Node.js 18+
- Docker & Docker Compose
- Git
- GitHub account
- Docker Hub account

### Local Development

1. **Clone the repository**

   ```bash
   git clone <your-repo-url>
   cd docker_prod
   ```

2. **Set up environment variables**

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Install dependencies**

   ```bash
   cd backend
   npm install
   ```

4. **Run locally with Node.js**

   ```bash
   npm run dev
   ```

5. **Or run with Docker**

   ```bash
   # Development mode with hot reload
   docker-compose -f docker-compose.dev.yml up --build

   # Production mode
   docker-compose up --build
   ```

6. **Access the application**
   - API: http://localhost:8000
   - Health check: http://localhost:8000/health

### Testing

```bash
cd backend
npm test          # Run tests
npm run lint      # Run linting
npm run lint:fix  # Fix linting issues
```

## 🔧 Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

```env
NODE_ENV=development
PORT=8000
# Add your specific configuration
```

### GitHub Secrets (Required)

Configure these secrets in your GitHub repository settings (Settings → Secrets and variables → Actions):

#### Docker Hub (Required for CI/CD)

- `DOCKER_USERNAME`: Your Docker Hub username
- `DOCKER_PASSWORD`: Your Docker Hub access token

**How to get Docker Hub credentials:**

1. Create account at [hub.docker.com](https://hub.docker.com)
2. Go to Account Settings → Security → New Access Token
3. Copy the token and use it as `DOCKER_PASSWORD`

That's it! No server setup required for this beginner-friendly pipeline.

## 🚀 How It Works

### Automatic CI/CD Pipeline

- **Push to any branch**: Runs tests and linting
- **Push to `develop`**: Builds and pushes Docker image with `develop` tag
- **Push to `main`**: Builds and pushes Docker image with `latest` tag + security scan

### Manual Docker Build

1. Go to GitHub Actions tab
2. Select "Manual Docker Build" workflow
3. Click "Run workflow"
4. Choose custom image tag
5. Optionally push to Docker Hub

### Using Your Docker Images

After the pipeline runs, you can use your images:

```bash
# Pull your image from Docker Hub
docker pull yourusername/taskflow-api:latest

# Run your container
docker run -p 8000:8000 yourusername/taskflow-api:latest

# Or use docker-compose locally
docker-compose up
```

### Server Setup

On your deployment servers:

1. **Install Docker & Docker Compose**

   ```bash
   # Ubuntu/Debian
   curl -fsSL https://get.docker.com -o get-docker.sh
   sh get-docker.sh
   sudo usermod -aG docker $USER
   ```

2. **Create deployment directory**

   ```bash
   # Production
   sudo mkdir -p /opt/docker-prod
   sudo chown $USER:$USER /opt/docker-prod

   # Staging
   sudo mkdir -p /opt/docker-prod-staging
   sudo chown $USER:$USER /opt/docker-prod-staging
   ```

3. **Clone repository**

   ```bash
   cd /opt/docker-prod
   git clone <your-repo-url> .
   ```

4. **Set up environment**

   ```bash
   cp .env.example .env
   # Edit .env with production values
   ```

5. **Set up SSH key access**
   ```bash
   # Add your GitHub Actions SSH public key to ~/.ssh/authorized_keys
   ```

## 📊 Monitoring

### Health Checks

- **Application**: `GET /health`
- **Docker**: Built-in health checks in containers
- **GitHub Actions**: Automated health verification after deployment

### Logs

```bash
# View application logs
docker-compose logs -f app

# View all logs
docker-compose logs -f
```

## 🔒 Security

- Non-root user in Docker containers
- Vulnerability scanning with Trivy
- Secrets management with GitHub Secrets
- Environment variable isolation
- Regular dependency updates

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes
4. Run tests: `npm test`
5. Commit changes: `git commit -am 'Add new feature'`
6. Push to branch: `git push origin feature/new-feature`
7. Submit a Pull Request

## 📝 API Endpoints

### Public Endpoints

- `GET /` - Welcome message and API info
- `GET /health` - Health check

### Authentication Endpoints

- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile (requires token)
- `POST /api/auth/logout` - User logout

### User Endpoints (Legacy)

- `GET /api/users` - Get users list
- `POST /api/users` - Create new user

### Example Usage

```bash
# Register a new user
curl -X POST http://localhost:8000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"name":"John Doe","email":"<EMAIL>","password":"password123"}'

# Login
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

## 🐛 Troubleshooting

### Common Issues

1. **GitHub Actions fails**

   - Check if Docker Hub secrets are set correctly
   - Verify Docker Hub username and token
   - Check the Actions tab for detailed error logs

2. **Docker build fails locally**

   - Make sure you're in the project root directory
   - Check if all dependencies are installed: `cd backend && npm install`
   - Verify Docker is running

3. **Tests fail**
   - Run tests locally first: `cd backend && npm test`
   - Check if all dependencies are installed
   - Make sure the application starts without errors

### Getting Help

- Check GitHub Issues
- Review GitHub Actions logs
- Check Docker container logs
- Verify environment configuration

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
