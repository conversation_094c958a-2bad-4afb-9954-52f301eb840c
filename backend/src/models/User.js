// Simple in-memory user storage for demo
// In production, this would be a proper database model

const bcrypt = require('bcryptjs');

class User {
  constructor(id, email, name, password) {
    this.id = id;
    this.email = email;
    this.name = name;
    this.password = password;
    this.createdAt = new Date().toISOString();
  }
}

// In-memory storage (replace with database in production)
const users = [];
let nextId = 1;

const UserModel = {
  // Create new user
  async create(userData) {
    const { email, name, password } = userData;

    // Check if user already exists
    const existingUser = users.find((user) => user.email === email);
    if (existingUser) {
      throw new Error('User already exists with this email');
    }

    // Hash password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create user
    const user = new User(nextId++, email, name, hashedPassword);
    users.push(user);

    // Return user without password
    const { password: _, ...userWithoutPassword } = user; // eslint-disable-line no-unused-vars
    return userWithoutPassword;
  },

  // Find user by email
  async findByEmail(email) {
    return users.find((user) => user.email === email);
  },

  // Find user by ID
  async findById(id) {
    const user = users.find((user) => user.id === parseInt(id));
    if (user) {
      const { password: _, ...userWithoutPassword } = user; // eslint-disable-line no-unused-vars
      return userWithoutPassword;
    }
    return null;
  },

  // Verify password
  async verifyPassword(plainPassword, hashedPassword) {
    return await bcrypt.compare(plainPassword, hashedPassword);
  },

  // Get all users (admin function)
  async getAll() {
    return users.map((user) => {
      const { password: _, ...userWithoutPassword } = user; // eslint-disable-line no-unused-vars
      return userWithoutPassword;
    });
  }
};

module.exports = UserModel;
