require("dotenv").config();
const express = require("express");
const cors = require("cors");
const helmet = require("helmet");

// Import routes
const authRoutes = require("./routes/auth");

const app = express();
const PORT = process.env.PORT || 8000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use("/api/auth", authRoutes);

app.get("/", (req, res) => {
	res.json({
		message: "Welcome to TaskFlow API!",
		description: "A powerful task management API with authentication",
		version: "1.0.0",
		environment: process.env.NODE_ENV || "development",
		timestamp: new Date().toISOString(),
		endpoints: {
			auth: "/api/auth",
			users: "/api/users",
			health: "/health",
		},
	});
});

app.get("/health", (req, res) => {
	res.status(200).json({
		status: "healthy",
		uptime: process.uptime(),
		timestamp: new Date().toISOString(),
		memory: process.memoryUsage(),
	});
});

app.get("/api/users", (req, res) => {
	// Sample API endpoint
	const users = [
		{ id: 1, name: "John Doe", email: "<EMAIL>" },
		{ id: 2, name: "Jane Smith", email: "<EMAIL>" },
		{ id: 3, name: "Bob Johnson", email: "<EMAIL>" },
	];

	res.json({
		success: true,
		data: users,
		count: users.length,
	});
});
// For Creating user
app.post("/api/users", (req, res) => {
	const { name, email } = req.body;

	if (!name || !email) {
		return res.status(400).json({
			success: false,
			error: "Name and email are required",
		});
	}

	const newUser = {
		id: Date.now(),
		name,
		email,
		createdAt: new Date().toISOString(),
	};

	res.status(201).json({
		success: true,
		data: newUser,
		message: "User created successfully",
	});
});

// Error handling middleware
app.use((err, req, res, _next) => {
	console.error(err.stack);
	res.status(500).json({
		success: false,
		error: "Something went wrong!",
		message:
			process.env.NODE_ENV === "development"
				? err.message
				: "Internal server error",
	});
});

// 404 handler
app.use("*", (req, res) => {
	res.status(404).json({
		success: false,
		error: "Route not found",
		path: req.originalUrl,
	});
});

// Start server only when this file is run directly (not when imported for testing)
if (require.main === module) {
	app.listen(PORT, "0.0.0.0", () => {
		console.log(`🚀 Server running on port ${PORT}`);
		console.log(`📝 Environment: ${process.env.NODE_ENV || "development"}`);
		console.log(`🔗 Health check: http://localhost:${PORT}/health`);
	});
}

module.exports = app;
