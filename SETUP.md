# 🚀 Simple Setup Guide - TaskFlow API

This guide will help you set up the TaskFlow API with Docker Hub CI/CD in just a few steps!

## 📋 Prerequisites

- GitHub account
- Docker Hub account (free at [hub.docker.com](https://hub.docker.com))
- Git installed on your computer
- Docker installed on your computer

## 🎯 Step-by-Step Setup

### Step 1: Fork or Clone This Repository

```bash
# Clone the repository
git clone https://github.com/yourusername/taskflow-api.git
cd taskflow-api

# Or if you forked it, clone your fork
git clone https://github.com/YOUR_USERNAME/taskflow-api.git
cd taskflow-api
```

### Step 2: Create Docker Hub Account & Token

1. **Create Docker Hub Account**
   - Go to [hub.docker.com](https://hub.docker.com)
   - Sign up for a free account
   - Remember your username!

2. **Create Access Token**
   - Go to Account Settings → Security
   - Click "New Access Token"
   - Name it "GitHub Actions"
   - Copy the token (you won't see it again!)

### Step 3: Set Up GitHub Secrets

1. **Go to your GitHub repository**
2. **Click Settings → Secrets and variables → Actions**
3. **Add these secrets:**

   | Secret Name | Value |
   |-------------|-------|
   | `DOCKER_USERNAME` | Your Docker Hub username |
   | `DOCKER_PASSWORD` | Your Docker Hub access token |

### Step 4: Test Locally (Optional)

```bash
# Install dependencies
cd backend
npm install

# Run tests
npm test

# Start the application
npm start

# Or use Docker
cd ..
docker-compose up --build
```

### Step 5: Push to GitHub and Watch the Magic! ✨

```bash
# Make a small change to test the pipeline
echo "# My TaskFlow API" >> README.md

# Commit and push
git add .
git commit -m "feat: test CI/CD pipeline"
git push origin main
```

**What happens next:**
1. 🧪 GitHub Actions runs tests
2. 🐳 Builds Docker image
3. 📦 Pushes to Docker Hub
4. ✅ You get a success notification!

### Step 6: Use Your Docker Image

After the pipeline succeeds, you can use your image anywhere:

```bash
# Pull your image
docker pull YOUR_DOCKER_USERNAME/taskflow-api:latest

# Run it
docker run -p 8000:8000 YOUR_DOCKER_USERNAME/taskflow-api:latest

# Test it
curl http://localhost:8000/health
```

## 🎉 You're Done!

Your TaskFlow API now has:
- ✅ Automated testing
- ✅ Docker image building
- ✅ Automatic publishing to Docker Hub
- ✅ Security scanning
- ✅ Professional CI/CD pipeline

## 🔄 Development Workflow

Now you can develop like a pro:

1. **Create feature branch**
   ```bash
   git checkout -b feature/my-new-feature
   ```

2. **Make changes and test**
   ```bash
   cd backend
   npm test
   npm run lint
   ```

3. **Commit and push**
   ```bash
   git add .
   git commit -m "feat: add amazing new feature"
   git push origin feature/my-new-feature
   ```

4. **Create Pull Request**
   - GitHub will automatically test your changes
   - Merge when ready
   - New Docker image gets built automatically!

## 🚀 Next Steps

- Add more API endpoints
- Integrate with a database
- Add frontend application
- Deploy to cloud platforms
- Set up monitoring and logging

## 🆘 Need Help?

- Check the main [README.md](README.md) for detailed documentation
- Look at the [Troubleshooting section](README.md#-troubleshooting)
- Create an issue in this repository
- Check GitHub Actions logs for detailed error messages

**Happy coding! 🎉**
