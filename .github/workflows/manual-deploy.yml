name: Manual Docker Build

on:
  workflow_dispatch:
    inputs:
      image_tag:
        description: "Docker image tag (default: manual-build)"
        required: false
        default: "manual-build"
        type: string
      push_to_registry:
        description: "Push to Docker Hub?"
        required: true
        default: true
        type: boolean

jobs:
  build:
    name: Manual Docker Build
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        if: ${{ github.event.inputs.push_to_registry == 'true' }}
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build Docker image
        run: |
          IMAGE_TAG="${{ github.event.inputs.image_tag }}"
          docker build -t taskflow-api:$IMAGE_TAG -f backend/Dockerfile .
          echo "✅ Built image: taskflow-api:$IMAGE_TAG"

      - name: Tag and Push to Docker Hub
        if: ${{ github.event.inputs.push_to_registry == 'true' }}
        run: |
          IMAGE_TAG="${{ github.event.inputs.image_tag }}"
          DOCKER_IMAGE="${{ secrets.DOCKER_USERNAME }}/taskflow-api:$IMAGE_TAG"

          docker tag taskflow-api:$IMAGE_TAG $DOCKER_IMAGE
          docker push $DOCKER_IMAGE

          echo "✅ Pushed to Docker Hub: $DOCKER_IMAGE"

      - name: Build Summary
        run: |
          echo "## 🐳 Docker Build Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Image Tag**: ${{ github.event.inputs.image_tag }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Built by**: ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Build Time**: $(date)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [ "${{ github.event.inputs.push_to_registry }}" == "true" ]; then
            echo "### 🚀 How to use this image:" >> $GITHUB_STEP_SUMMARY
            echo '```bash' >> $GITHUB_STEP_SUMMARY
            echo "# Pull the image" >> $GITHUB_STEP_SUMMARY
            echo "docker pull ${{ secrets.DOCKER_USERNAME }}/taskflow-api:${{ github.event.inputs.image_tag }}" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "# Run the container" >> $GITHUB_STEP_SUMMARY
            echo "docker run -p 8000:8000 ${{ secrets.DOCKER_USERNAME }}/taskflow-api:${{ github.event.inputs.image_tag }}" >> $GITHUB_STEP_SUMMARY
            echo '```' >> $GITHUB_STEP_SUMMARY
          else
            echo "### 📦 Local build completed (not pushed to registry)" >> $GITHUB_STEP_SUMMARY
          fi
